// Main Application - Complete Omni Platform Suite
export default defineNuxtConfig({
  compatibilityDate: '2025-01-08',
  // Layer configuration for complete platform
  extends: [
    '../../layers/shared', // Authentication & utilities
    '../../layers/tairo', // Component library
    '../../layers/modules/crm', // CRM module
    '../../layers/modules/writing', // Writing module
    '../../layers/modules/ai-chat', // AI Chat module
  ],

  // Modules
  modules: [
    '@nuxtjs/i18n',
  ],

  // Application metadata
  app: {
    head: {
      title: 'Omni Platform - Complete Business Suite',
      meta: [
        {
          name: 'description',
          content: 'Complete business platform with CRM, content writing, and AI assistance',
        },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      ],
    },
  },

  // Routing configuration
  routeRules: {
    '/': { redirect: '/dashboard' },
    '/dashboard': { ssr: false }, // SPA for dynamic dashboard
    '/crm/**': { ssr: false }, // CRM routes as SPA
    '/writing/**': { ssr: false }, // Writing routes as SPA
    '/chat/**': { ssr: false }, // Chat routes as SPA
  },

  // Runtime configuration
  runtimeConfig: {
    public: {
      appType: 'main',
      appName: 'Omni Platform',
      availableModules: ['crm', 'writing', 'ai-chat'],
      features: {
        crossModuleIntegration: true,
        advancedAnalytics: true,
        adminControls: true,
      },
      branding: {
        primaryColor: '#6366f1', // Indigo
        logo: '/omni-logo.svg',
        favicon: '/favicon.ico',
      },
      analytics: {
        enabled: true,
        trackingId: process.env.ANALYTICS_MAIN_APP || '',
        customDimensions: {
          appType: 'main',
        },
      },
    },
  },

  // Build optimization with memory management
  vite: {
    build: {
      sourcemap: false, // Disable sourcemaps for production builds
      rollupOptions: {
        output: {
          manualChunks(id) {
            // Core chunks
            if (id.includes('vue'))
              return 'vendor-vue'
            if (id.includes('firebase'))
              return 'vendor-firebase'
            if (id.includes('@headlessui/vue') || id.includes('@heroicons/vue'))
              return 'vendor-ui'

            // Module-specific chunks
            if (id.includes('apexcharts') || id.includes('vue3-apexcharts'))
              return 'crm-charts'
            if (id.includes('layers/modules/crm/components'))
              return 'crm-components'
            if (id.includes('layers/modules/writing/components'))
              return 'writing-components'

            // Shared components
            if (id.includes('layers/shared/components'))
              return 'shared-components'
            if (id.includes('layers/tairo/components'))
              return 'tairo-components'
          },
        },
      },
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
          passes: 2,
        },
      },
    },
    optimizeDeps: {
      include: ['vue', 'vue-router', '@nuxt/kit'],
    },
  },

  // Build configuration for memory management
  build: {
    transpile: ['@tailwindcss/vite'],
  },

  // CSS configuration
  css: ['~/assets/main.css'],

  // Nitro server configuration
  nitro: {
    prerender: {
      routes: ['/'],
    },
    firebase: {
      gen: 2,
      functions: {
        maxInstances: 10,
      },
    },
  },

  // TypeScript
  typescript: {
    strict: true,
    typeCheck: false, // Temporarily disabled for development
  },

  // Internationalization
  i18n: {
    baseUrl: '/',
    strategy: 'no_prefix',
    defaultLocale: 'en',
    lazy: true,
    locales: [
      { code: 'en', dir: 'ltr', language: 'en-US', file: 'en-US.yaml', name: 'English', isCatchallLocale: true },
      { code: 'fr', dir: 'ltr', language: 'fr-FR', file: 'fr-FR.yaml', name: 'Français' },
      { code: 'es', dir: 'ltr', language: 'es-ES', file: 'es-ES.yaml', name: 'Español' },
      { code: 'de', dir: 'ltr', language: 'de-DE', file: 'de-DE.yaml', name: 'Deutsch' },
      { code: 'ar', dir: 'rtl', language: 'ar-SA', file: 'ar-SA.yaml', name: 'العربية' },
      { code: 'ja', dir: 'ltr', language: 'ja-JP', file: 'ja-JP.yaml', name: '日本語' },
    ],
    experimental: {
      generatedLocaleFilePathFormat: 'off',
    },
    bundle: {
      optimizeTranslationDirective: false,
    },
  },

  // Development
  devtools: { enabled: true },
})
