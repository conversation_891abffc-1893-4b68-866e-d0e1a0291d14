<template>
  <nav class="main-app-navigation">
    <!-- <PERSON><PERSON> and App Title -->
    <div class="flex items-center justify-between px-6 py-4 border-b border-muted-200 dark:border-muted-800">
      <div class="flex items-center gap-3">
        <img 
          :src="$config.public.branding.logo" 
          alt="Omni Platform"
          class="h-8 w-auto"
        >
        <BaseHeading size="lg" weight="semibold">
          {{ $config.public.appName }}
        </BaseHeading>
      </div>
      <!-- App Switcher -->
      <BaseDropdown>
        <template #trigger>
          <BaseButton size="sm" variant="ghost">
            <Icon name="solar:widget-linear" class="size-4" />
            <span>Switch App</span>
          </BaseButton>
        </template>
        <BaseDropdownMenu>
          <BaseDropdownItem href="https://crm.omni.com">
            <Icon name="solar:chart-2-linear" class="size-4" />
            <span>CRM Only</span>
          </BaseDropdownItem>
          <BaseDropdownItem href="https://writer.omni.com">
            <Icon name="solar:pen-linear" class="size-4" />
            <span>Writer Only</span>
          </BaseDropdownItem>
        </BaseDropdownMenu>
      </BaseDropdown>
    </div>
    <!-- Main Navigation -->
    <div class="px-4 py-2">
      <!-- Dashboard -->
      <NuxtLink 
        to="/dashboard"
        class="nav-item"
        :class="{ active: $route.path === '/dashboard' }"
      >
        <Icon name="solar:chart-square-linear" class="size-5" />
        <span>Dashboard</span>
      </NuxtLink>
      <!-- CRM Section -->
      <div class="nav-section">
        <BaseText size="xs" weight="medium" class="text-muted-500 uppercase px-2 mb-2">
          CRM
        </BaseText>
        <NuxtLink 
          to="/crm/dashboard"
          class="nav-item"
          :class="{ active: $route.path.startsWith('/crm/dashboard') }"
        >
          <Icon name="solar:chart-2-linear" class="size-5" />
          <span>Sales Dashboard</span>
        </NuxtLink>
        <NuxtLink 
          to="/crm/customers"
          class="nav-item"
          :class="{ active: $route.path.startsWith('/crm/customers') }"
        >
          <Icon name="solar:users-group-rounded-linear" class="size-5" />
          <span>Customers</span>
        </NuxtLink>
        <NuxtLink 
          to="/crm/leads/kanban"
          class="nav-item"
          :class="{ active: $route.path.startsWith('/crm/leads') }"
        >
          <Icon name="solar:layers-linear" class="size-5" />
          <span>Lead Pipeline</span>
        </NuxtLink>
      </div>
      <!-- Writing Section -->
      <div class="nav-section">
        <BaseText size="xs" weight="medium" class="text-muted-500 uppercase px-2 mb-2">
          Writing
        </BaseText>
        <NuxtLink 
          to="/writing/dashboard"
          class="nav-item"
          :class="{ active: $route.path === '/writing/dashboard' }"
        >
          <Icon name="solar:document-text-linear" class="size-5" />
          <span>Content Dashboard</span>
        </NuxtLink>
        <NuxtLink 
          to="/writing/articles"
          class="nav-item"
          :class="{ active: $route.path.startsWith('/writing/articles') }"
        >
          <Icon name="solar:notebook-linear" class="size-5" />
          <span>Articles</span>
        </NuxtLink>
        <NuxtLink 
          to="/writing/articles/new"
          class="nav-item"
          :class="{ active: $route.path === '/writing/articles/new' }"
        >
          <Icon name="solar:pen-new-square-linear" class="size-5" />
          <span>New Article</span>
        </NuxtLink>
      </div>
      <!-- AI Chat Section -->
      <div class="nav-section">
        <BaseText size="xs" weight="medium" class="text-muted-500 uppercase px-2 mb-2">
          AI Assistant
        </BaseText>
        <NuxtLink 
          to="/chat"
          class="nav-item"
          :class="{ active: $route.path.startsWith('/chat') }"
        >
          <Icon name="solar:chat-round-dots-linear" class="size-5" />
          <span>AI Chat</span>
        </NuxtLink>
        <NuxtLink 
          to="/chat/history"
          class="nav-item"
          :class="{ active: $route.path === '/chat/history' }"
        >
          <Icon name="solar:history-linear" class="size-5" />
          <span>Chat History</span>
        </NuxtLink>
        <NuxtLink 
          to="/chat/settings"
          class="nav-item"
          :class="{ active: $route.path === '/chat/settings' }"
        >
          <Icon name="solar:settings-linear" class="size-5" />
          <span>AI Settings</span>
        </NuxtLink>
      </div>
      <!-- Admin Section -->
      <!-- <div class="nav-section" v-if="hasRole('admin')">
        <BaseText size="xs" weight="medium" class="text-muted-500 uppercase px-2 mb-2">
          Administration
        </BaseText>
        <NuxtLink 
          to="/admin/users"
          class="nav-item"
          :class="{ active: $route.path.startsWith('/admin/users') }"
        >
          <Icon name="solar:user-id-linear" class="size-5" />
          <span>User Management</span>
        </NuxtLink>
        <NuxtLink 
          to="/admin/settings"
          class="nav-item"
          :class="{ active: $route.path.startsWith('/admin/settings') }"
        >
          <Icon name="solar:settings-minimalistic-linear" class="size-5" />
          <span>Platform Settings</span>
        </NuxtLink>
      </div> -->
    </div>
  </nav>
</template>
<script setup lang="ts">
// const { hasRole } = useAuth()
const { $config } = useNuxtApp()
const route = useRoute()
</script>
<style scoped>
.main-app-navigation {
  background-color: rgb(255 255 255);
  height: 100%;
  overflow-y: auto;
}
.nav-section {
  margin-top: 1.5rem;
}
.nav-item {
  color: rgb(64 64 64);
  background-color: rgb(23 23 23);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding-inline: 0.75rem;
  padding-block: 0.5rem;
  border-radius: 0.5rem;
  margin-bottom: 0.25rem;
}
.nav-item.active {
  color: rgb(13 148 136);
  font-weight: 500;
}
</style>